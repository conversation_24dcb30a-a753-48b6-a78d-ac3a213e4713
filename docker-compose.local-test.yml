version: '3.8'

# Local Testing Environment - Mirrors Production Setup
# This compose file replicates the production orchestration for local testing
# Uses local builds instead of ECR images for development/testing

services:
  # Backend API service - Local Test
  api:
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
      target: runtime
    image: yendorcats/api:local-test
    container_name: yendorcats-api-local-test
    restart: unless-stopped
    ports:
      - "5003:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production  # Use Production settings for testing
      - ASPNETCORE_URLS=http://+:80
      - AWS__Region=us-west-004
      - AWS__UseCredentialsFromSecrets=false
      - AWS__S3__BucketName=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS__S3__UseDirectS3Urls=true
      - AWS__S3__ServiceUrl=https://s3.us-west-004.backblazeb2.com
      - AWS__S3__PublicUrl=https://f004.backblazeb2.com/file/${AWS_S3_BUCKET_NAME:-yendor}/{key}
      - AWS__S3__UseCdn=true
      - AWS__S3__AccessKey=${AWS_S3_ACCESS_KEY}
      - AWS__S3__SecretKey=${AWS_S3_SECRET_KEY}
      - AWS__S3__KeyPrefix=YendorCats-General-SiteAccess/
      - B2_APPLICATION_KEY_ID=${B2_APPLICATION_KEY_ID}
      - B2_APPLICATION_KEY=${B2_APPLICATION_KEY}
      - B2_BUCKET_ID=${B2_BUCKET_ID}
      - ConnectionStrings__DefaultConnection=Server=db;Database=YendorCats;User=${MYSQL_USER};Password=${MYSQL_PASSWORD};Port=3306;
      - JwtSettings__Secret=${YENDOR_JWT_SECRET}
      # CORS Configuration for local testing
      - SERVER__ExternalIP=localhost
      - CORS__AdditionalOrigins=http://localhost,http://localhost:80,http://localhost:8080
    volumes:
      - api-data-local-test:/app/data
      - api-logs-local-test:/app/Logs
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - yendorcats-local-test

  # MariaDB Database - Local Test (mirrors production)
  db:
    image: mariadb:10.11
    container_name: yendorcats-db-local-test
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-localtest123}
      - MYSQL_USER=${MYSQL_USER:-yendoruser}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD:-localtest456}
      - MYSQL_DATABASE=YendorCats
    volumes:
      - mariadb-data-local-test:/var/lib/mysql
    ports:
      - "3307:3306"  # Different port to avoid conflicts with existing MySQL
    networks:
      - yendorcats-local-test
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-localtest123}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # File Upload Service - Local Test
  uploader:
    build:
      context: ./tools/file-uploader
      dockerfile: Dockerfile
    image: yendorcats/uploader:local-test
    container_name: yendorcats-uploader-local-test
    restart: unless-stopped
    ports:
      - "5002:80"
    environment:
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME:-yendor}
      - AWS_S3_REGION=us-west-004
      - AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com
      - AWS_S3_ACCESS_KEY=${AWS_S3_ACCESS_KEY}
      - AWS_S3_SECRET_KEY=${AWS_S3_SECRET_KEY}
      - API_BASE_URL=http://api
    depends_on:
      api:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-local-test

  # Frontend with Nginx - Local Test (mirrors production)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    image: yendorcats/frontend:local-test
    container_name: yendorcats-frontend-local-test
    ports:
      - "80:80"
    depends_on:
      api:
        condition: service_healthy
      uploader:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - yendorcats-local-test
    environment:
      - API_HOST=api
      - UPLOADER_HOST=uploader
      - NGINX_CONFIG=production

networks:
  yendorcats-local-test:
    driver: bridge
    name: yendorcats-local-test

volumes:
  api-data-local-test:
    driver: local
  api-logs-local-test:
    driver: local
  mariadb-data-local-test:
    driver: local
