# YendorCats Local Testing Environment

This guide provides a complete local testing environment that **mirrors your production server orchestration** for testing purposes. The setup replicates the exact same services, configurations, and deployment patterns used in production.

## 🎯 Overview

The local testing environment includes:
- **Backend API** (.NET Core) - Mirrors production configuration
- **MariaDB Database** - Same version and configuration as production
- **File Uploader Service** - Node.js microservice for file uploads
- **Frontend** - Nginx serving static files with API proxy
- **Health Checks** - Same monitoring as production
- **Deployment Scripts** - Mirror production deployment process

## 📋 Prerequisites

- **Docker Desktop** installed and running
- **Docker Compose** available
- **Backblaze B2 credentials** (same as production)
- **Git** (for cloning/updating)

## 🚀 Quick Start

### 1. Initial Setup

```bash
# Run the setup script
./scripts/setup-local-test.sh
```

This will:
- Check prerequisites
- Create environment file (`.env.local-test`)
- Generate secure JWT secret
- Create necessary directories

### 2. Configure Credentials

Edit `.env.local-test` and add your Backblaze B2 credentials:

```bash
# Edit the environment file
nano .env.local-test

# Add your actual B2 credentials:
AWS_S3_BUCKET_NAME=your-bucket-name
AWS_S3_ACCESS_KEY=your-b2-application-key-id
AWS_S3_SECRET_KEY=your-b2-application-key
B2_APPLICATION_KEY_ID=your-b2-application-key-id
B2_APPLICATION_KEY=your-b2-application-key
B2_BUCKET_ID=your-b2-bucket-id
```

### 3. Deploy the Environment

```bash
# Deploy the complete environment
./scripts/deploy-local-test.sh
```

### 4. Access Your Application

- **Frontend**: http://localhost
- **API**: http://localhost:5003
- **Uploader**: http://localhost:5002
- **Database**: localhost:3307

## 🛠️ Management Commands

### Deployment Commands

```bash
# Deploy/redeploy the environment
./scripts/deploy-local-test.sh deploy

# Stop all containers
./scripts/deploy-local-test.sh stop

# Restart all containers
./scripts/deploy-local-test.sh restart

# View live logs
./scripts/deploy-local-test.sh logs

# Check container status
./scripts/deploy-local-test.sh status

# Run health checks
./scripts/deploy-local-test.sh health

# Clean up old containers/images
./scripts/deploy-local-test.sh cleanup
```

### Docker Compose Commands

```bash
# Manual Docker Compose operations
docker-compose -f docker-compose.local-test.yml --env-file .env.local-test up -d
docker-compose -f docker-compose.local-test.yml --env-file .env.local-test down
docker-compose -f docker-compose.local-test.yml --env-file .env.local-test logs -f
```

## 🔍 Troubleshooting

### Common Issues

**1. Port Conflicts**
```bash
# Check what's using ports
lsof -i :80 -i :5002 -i :5003 -i :3307

# Stop conflicting services
sudo lsof -ti:80 | xargs kill -9
```

**2. Database Connection Issues**
```bash
# Check database logs
docker-compose -f docker-compose.local-test.yml logs db

# Connect to database directly
docker exec -it yendorcats-db-local-test mysql -u yendoruser -p
```

**3. API Health Check Failures**
```bash
# Check API logs
docker-compose -f docker-compose.local-test.yml logs api

# Test API directly
curl -v http://localhost:5003/health
```

**4. Build Failures**
```bash
# Clean Docker cache and rebuild
docker system prune -a
./scripts/deploy-local-test.sh deploy
```

### Log Locations

- **Deployment logs**: `./logs/local-test-deploy.log`
- **Container logs**: `docker-compose logs <service>`
- **API logs**: Mounted to `api-logs-local-test` volume

## 🔧 Configuration Details

### Environment Variables

The `.env.local-test` file contains all configuration matching production:

| Variable | Description | Example |
|----------|-------------|---------|
| `MYSQL_ROOT_PASSWORD` | Database root password | `localtest123` |
| `MYSQL_USER` | Database user | `yendoruser` |
| `MYSQL_PASSWORD` | Database password | `localtest456` |
| `YENDOR_JWT_SECRET` | JWT signing secret | Auto-generated |
| `AWS_S3_BUCKET_NAME` | B2 bucket name | `yendor` |
| `AWS_S3_ACCESS_KEY` | B2 application key ID | Your B2 key |
| `AWS_S3_SECRET_KEY` | B2 application key | Your B2 secret |

### Service Configuration

**API Service** (`api`):
- Port: 5003
- Environment: Production (for testing)
- Database: MariaDB connection
- Storage: Backblaze B2
- Health check: `/health` endpoint

**Database Service** (`db`):
- Port: 3307 (to avoid conflicts)
- Version: MariaDB 10.11
- Character set: utf8mb4
- Health check: mysqladmin ping

**Uploader Service** (`uploader`):
- Port: 5002
- Environment: Production
- Storage: Backblaze B2
- Health check: `/health` endpoint

**Frontend Service** (`frontend`):
- Port: 80
- Server: Nginx
- Configuration: Production mode
- Health check: Root endpoint

### Network Configuration

- **Network**: `yendorcats-local-test`
- **Driver**: Bridge
- **Internal communication**: Service names (api, db, uploader)
- **External access**: Mapped ports

### Volume Configuration

- **Database**: `mariadb-data-local-test`
- **API Data**: `api-data-local-test`
- **API Logs**: `api-logs-local-test`

## 🔄 Backup and Recovery

### Automatic Backups

The deployment script automatically creates backups:
- **Location**: `./backups/local-test/`
- **Contents**: Database, configuration files
- **Retention**: Last 5 backups kept

### Manual Backup

```bash
# Create backup manually
./scripts/deploy-local-test.sh backup

# Backup location
ls -la ./backups/local-test/
```

## 🎯 Testing Scenarios

### 1. Production Deployment Testing

Test the exact deployment process used in production:

```bash
# Deploy with fresh build
./scripts/deploy-local-test.sh deploy

# Verify all services
./scripts/deploy-local-test.sh health
```

### 2. Database Migration Testing

Test database migrations and data persistence:

```bash
# Deploy initial version
./scripts/deploy-local-test.sh deploy

# Add test data via API
curl -X POST http://localhost:5003/api/test-data

# Redeploy and verify data persistence
./scripts/deploy-local-test.sh deploy
```

### 3. Service Integration Testing

Test communication between services:

```bash
# Test API → Database
curl http://localhost:5003/api/gallery

# Test Frontend → API
curl http://localhost/api/gallery

# Test Uploader → API
curl -X POST http://localhost:5002/upload
```

## 📊 Monitoring

### Health Checks

All services include health checks matching production:

```bash
# Check all service health
./scripts/deploy-local-test.sh health

# Individual service checks
curl http://localhost:5003/health    # API
curl http://localhost:5002/health    # Uploader
curl http://localhost/health         # Frontend
```

### Performance Monitoring

```bash
# Monitor resource usage
docker stats

# Monitor specific containers
docker stats yendorcats-api-local-test yendorcats-db-local-test
```

## 🔐 Security Notes

- **JWT Secret**: Auto-generated secure secret
- **Database**: Isolated network, non-standard port
- **CORS**: Configured for localhost only
- **Environment**: Production security settings

## 📝 Development Workflow

1. **Make changes** to your code
2. **Test locally** using this environment
3. **Deploy to staging** using production scripts
4. **Deploy to production** with confidence

This local testing environment ensures your changes work exactly as they will in production!

---

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review logs: `./logs/local-test-deploy.log`
3. Check container logs: `docker-compose logs <service>`
4. Verify environment configuration: `.env.local-test`

The local testing environment is designed to be as close to production as possible, giving you confidence in your deployments!
