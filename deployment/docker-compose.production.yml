version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend/YendorCats.API/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_URLS=http://+:8080
      - YENDOR_S3_ACCESS_KEY=${YENDOR_S3_ACCESS_KEY}
      - YENDOR_S3_SECRET_KEY=${YENDOR_S3_SECRET_KEY}
      - YENDOR_DEFAULT_ADMIN_USERNAME=${YENDOR_DEFAULT_ADMIN_USERNAME}
      - YENDOR_DEFAULT_ADMIN_EMAIL=${YENDOR_DEFAULT_ADMIN_EMAIL}
      - YENDOR_DEFAULT_ADMIN_PASSWORD=${YENDOR_DEFAULT_ADMIN_PASSWORD}
      - YENDOR_JWT_SECRET=${YENDOR_JWT_SECRET}
    networks:
      - yendorcats-network

  frontend:
    image: 025066273203.dkr.ecr.ap-southeast-2.amazonaws.com/yendorcats/frontend:latest
    container_name: yendorcats-frontend-production
    ports:
      - "80:80"
    environment:
      - NGINX_CONFIG=production
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - yendorcats-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  yendorcats-network:
    driver: bridge
