#!/bin/bash

# Build and Push Docker Images to AWS ECR (macOS optimized)
# Based on .github/workflows/frontend-ci.yml

set -e  # Exit on error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Parse command line arguments
NO_CACHE=""
for arg in "$@"
do
    case $arg in
        --no-cache)
        NO_CACHE="--no-cache"
        shift # Remove --no-cache from processing
        ;;
    esac
done

echo -e "${BLUE}=== Docker Build and Push to AWS ECR (macOS) ===${NC}"
echo -e "${YELLOW}This script will build and push all three services to AWS ECR${NC}"
if [ -n "$NO_CACHE" ]; then
    echo -e "${YELLOW}⚠️  Running with --no-cache: Docker build cache will be disabled${NC}"
fi
echo ""

# Function to mask sensitive input
mask_input() {
    local prompt="$1"
    local var_name="$2"
    echo -n "$prompt"
    read -s value
    echo ""
    eval "$var_name='$value'"
}

# Load environment variables from .env file if it exists
if [ -f ".env" ]; then
    echo -e "${BLUE}Loading environment variables from .env file...${NC}"
    # Export variables from .env file, handling comments and empty lines
    export $(grep -v '^#' .env | grep -v '^$' | xargs)
    echo -e "${GREEN}✓ Environment variables loaded${NC}"
    echo ""
fi

# Check for existing AWS credentials in environment variables
echo -e "${BLUE}Checking for AWS credentials...${NC}"

# AWS Account ID (check AWS_ECR_ACCOUNT_ID first)
if [ -z "$AWS_ACCOUNT_ID" ]; then
    if [ -n "$AWS_ECR_ACCOUNT_ID" ]; then
        AWS_ACCOUNT_ID="$AWS_ECR_ACCOUNT_ID"
        echo -e "${GREEN}✓ Using AWS Account ID from AWS_ECR_ACCOUNT_ID environment variable${NC}"
    else
        echo -n "Enter AWS Account ID: "
        read AWS_ACCOUNT_ID
    fi
else
    echo -e "${GREEN}✓ Using AWS Account ID from environment: ${AWS_ACCOUNT_ID}${NC}"
fi

# AWS Access Key ID (check AWS_ECR_* variables first)
if [ -z "$AWS_ACCESS_KEY_ID" ]; then
    if [ -n "$AWS_ECR_S3_ACCESS_KEY" ]; then
        AWS_ACCESS_KEY_ID="$AWS_ECR_S3_ACCESS_KEY"
        echo -e "${GREEN}✓ Using AWS Access Key ID from AWS_ECR_S3_ACCESS_KEY environment variable${NC}"
    elif [ -n "$AWS_S3_ACCESS_KEY" ]; then
        AWS_ACCESS_KEY_ID="$AWS_S3_ACCESS_KEY"
        echo -e "${GREEN}✓ Using AWS Access Key ID from AWS_S3_ACCESS_KEY environment variable${NC}"
    else
        echo -e "${YELLOW}AWS credentials not found in environment variables${NC}"
        echo "(These will be used only for this session and not stored)"
        echo ""
        mask_input "Enter AWS Access Key ID: " AWS_ACCESS_KEY_ID
    fi
else
    echo -e "${GREEN}✓ Using AWS Access Key ID from environment${NC}"
fi

# AWS Secret Access Key (check AWS_ECR_* variables first)
if [ -z "$AWS_SECRET_ACCESS_KEY" ]; then
    if [ -n "$AWS_ECR_S3_SECRET_KEY" ]; then
        AWS_SECRET_ACCESS_KEY="$AWS_ECR_S3_SECRET_KEY"
        echo -e "${GREEN}✓ Using AWS Secret Access Key from AWS_ECR_S3_SECRET_KEY environment variable${NC}"
    elif [ -n "$AWS_S3_SECRET_KEY" ]; then
        AWS_SECRET_ACCESS_KEY="$AWS_S3_SECRET_KEY"
        echo -e "${GREEN}✓ Using AWS Secret Access Key from AWS_S3_SECRET_KEY environment variable${NC}"
    else
        mask_input "Enter AWS Secret Access Key: " AWS_SECRET_ACCESS_KEY
    fi
else
    echo -e "${GREEN}✓ Using AWS Secret Access Key from environment${NC}"
fi

# AWS Region (check AWS_ECR_REGION first)
if [ -z "$AWS_REGION" ]; then
    if [ -n "$AWS_ECR_REGION" ]; then
        AWS_REGION="$AWS_ECR_REGION"
        echo -e "${GREEN}✓ Using AWS Region from AWS_ECR_REGION environment variable: ${AWS_REGION}${NC}"
    else
        echo -n "Enter AWS Region [default: ap-southeast-2]: "
        read AWS_REGION
        AWS_REGION=${AWS_REGION:-ap-southeast-2}
    fi
else
    echo -e "${GREEN}✓ Using AWS Region from environment: ${AWS_REGION}${NC}"
fi

# Export AWS credentials for this session
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_REGION
export AWS_DEFAULT_REGION=${AWS_REGION}

# Set ECR registry URL
ECR_REGISTRY="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com"

echo ""
echo -e "${GREEN}✓ AWS credentials configured${NC}"
echo -e "Account ID: ${AWS_ACCOUNT_ID}"
echo -e "Region: ${AWS_REGION}"
echo -e "ECR Registry: ${ECR_REGISTRY}"
echo ""

# Test AWS credentials first
echo -e "${BLUE}Testing AWS credentials...${NC}"
aws sts get-caller-identity --region ${AWS_REGION} > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ AWS credentials are valid${NC}"
else
    echo -e "${RED}✗ Invalid AWS credentials or permissions${NC}"
    echo "Please check your credentials and try again"
    exit 1
fi

# Get ECR login token
echo ""
echo -e "${BLUE}Getting ECR authentication token...${NC}"
ECR_TOKEN=$(aws ecr get-login-password --region ${AWS_REGION} 2>&1)

if [ $? -ne 0 ]; then
    echo -e "${RED}✗ Failed to get ECR login token${NC}"
    echo "Error: ${ECR_TOKEN}"
    exit 1
fi

echo -e "${GREEN}✓ ECR token obtained${NC}"

# Login to ECR using the token directly (avoiding credential helper issues)
echo ""
echo -e "${BLUE}Logging into ECR...${NC}"

# Create a temporary Docker config that bypasses credential helpers
TEMP_DOCKER_CONFIG=$(mktemp -d)
export DOCKER_CONFIG=${TEMP_DOCKER_CONFIG}

echo "${ECR_TOKEN}" | docker login --username AWS --password-stdin ${ECR_REGISTRY}

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Successfully logged into ECR${NC}"
else
    echo -e "${RED}✗ Failed to login to ECR${NC}"
    echo "Possible issues:"
    echo "  1. Docker Desktop is not running"
    echo "  2. Network connectivity issues"
    echo "  3. ECR endpoint is not accessible"
    rm -rf ${TEMP_DOCKER_CONFIG}
    exit 1
fi

# Get Git commit SHA
echo ""
echo -e "${BLUE}Getting Git commit information...${NC}"
GIT_SHA=$(git rev-parse HEAD)
GIT_SHA_SHORT=$(git rev-parse --short HEAD)
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

echo "Full SHA: ${GIT_SHA}"
echo "Short SHA: ${GIT_SHA_SHORT}"
echo "Branch: ${GIT_BRANCH}"

# Function to build and tag image
build_and_tag() {
    local service_name=$1
    local dockerfile=$2
    local context=$3
    
    echo ""
    echo -e "${BLUE}Building ${service_name}...${NC}"
    echo "Dockerfile: ${dockerfile}"
    echo "Context: ${context}"
    
    # Build with multiple tags
    docker build ${NO_CACHE} \
        -f ${dockerfile} \
        -t ${ECR_REGISTRY}/${service_name}:latest \
        -t ${ECR_REGISTRY}/${service_name}:${GIT_SHA_SHORT} \
        -t ${ECR_REGISTRY}/${service_name}:${GIT_BRANCH} \
        ${context}
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Successfully built ${service_name}${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to build ${service_name}${NC}"
        return 1
    fi
}

# Function to push image with all tags
push_image() {
    local service_name=$1
    
    echo ""
    echo -e "${BLUE}Pushing ${service_name} to ECR...${NC}"
    
    # Push all tags
    for tag in latest ${GIT_SHA_SHORT} ${GIT_BRANCH}; do
        echo "Pushing ${ECR_REGISTRY}/${service_name}:${tag}"
        docker push ${ECR_REGISTRY}/${service_name}:${tag}
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ Pushed ${service_name}:${tag}${NC}"
        else
            echo -e "${RED}✗ Failed to push ${service_name}:${tag}${NC}"
            return 1
        fi
    done
    
    return 0
}

# Check if ECR repositories exist
echo ""
echo -e "${BLUE}Checking ECR repositories...${NC}"
for repo in yendorcats-api yendorcats-frontend yendorcats-uploader; do
    aws ecr describe-repositories --repository-names ${repo} --region ${AWS_REGION} &> /dev/null
    if [ $? -ne 0 ]; then
        echo -e "${YELLOW}Repository ${repo} does not exist. Creating...${NC}"
        aws ecr create-repository --repository-name ${repo} --region ${AWS_REGION} > /dev/null
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✓ Created repository ${repo}${NC}"
        else
            echo -e "${RED}✗ Failed to create repository ${repo}${NC}"
            rm -rf ${TEMP_DOCKER_CONFIG}
            exit 1
        fi
    else
        echo -e "${GREEN}✓ Repository ${repo} exists${NC}"
    fi
done

# Build all images
echo ""
echo -e "${YELLOW}=== Building Docker Images ===${NC}"

# Build API
if ! build_and_tag "yendorcats-api" "backend/YendorCats.API/Dockerfile" "."; then
    echo -e "${RED}Failed to build API image${NC}"
    rm -rf ${TEMP_DOCKER_CONFIG}
    exit 1
fi

# Build Frontend (requires special handling for .git directory)
echo -e "${YELLOW}Note: Frontend build requires .git for version stamping${NC}"

# Temporarily modify .dockerignore to allow .git for frontend build
if grep -q "^\.git$" .dockerignore 2>/dev/null; then
    echo "Temporarily removing .git from .dockerignore for frontend build..."
    cp .dockerignore .dockerignore.backup
    grep -v "^\.git$" .dockerignore > .dockerignore.tmp && mv .dockerignore.tmp .dockerignore
    DOCKERIGNORE_MODIFIED=true
else
    DOCKERIGNORE_MODIFIED=false
fi

if ! build_and_tag "yendorcats-frontend" "Dockerfile.frontend.ci" "."; then
    echo -e "${RED}Failed to build Frontend image${NC}"
    # Restore .dockerignore if it was modified
    if [ "$DOCKERIGNORE_MODIFIED" = true ]; then
        mv .dockerignore.backup .dockerignore
        echo "Restored original .dockerignore"
    fi
    rm -rf ${TEMP_DOCKER_CONFIG}
    exit 1
fi

# Restore .dockerignore if it was modified
if [ "$DOCKERIGNORE_MODIFIED" = true ]; then
    mv .dockerignore.backup .dockerignore
    echo "Restored original .dockerignore"
fi

# Build Uploader
if ! build_and_tag "yendorcats-uploader" "tools/file-uploader/Dockerfile" "tools/file-uploader"; then
    echo -e "${RED}Failed to build Uploader image${NC}"
    rm -rf ${TEMP_DOCKER_CONFIG}
    exit 1
fi

# Push all images
echo ""
echo -e "${YELLOW}=== Pushing Images to ECR ===${NC}"

# Push API
if ! push_image "yendorcats-api"; then
    echo -e "${RED}Failed to push API image${NC}"
    rm -rf ${TEMP_DOCKER_CONFIG}
    exit 1
fi

# Push Frontend
if ! push_image "yendorcats-frontend"; then
    echo -e "${RED}Failed to push Frontend image${NC}"
    rm -rf ${TEMP_DOCKER_CONFIG}
    exit 1
fi

# Push Uploader
if ! push_image "yendorcats-uploader"; then
    echo -e "${RED}Failed to push Uploader image${NC}"
    rm -rf ${TEMP_DOCKER_CONFIG}
    exit 1
fi

# Display final results
echo ""
echo -e "${GREEN}=== Successfully Built and Pushed All Images ===${NC}"
echo ""
echo -e "${BLUE}Final ECR Image URIs:${NC}"
echo ""
echo "API Service:"
echo "  Latest: ${ECR_REGISTRY}/yendorcats-api:latest"
echo "  SHA: ${ECR_REGISTRY}/yendorcats-api:${GIT_SHA_SHORT}"
echo "  Branch: ${ECR_REGISTRY}/yendorcats-api:${GIT_BRANCH}"
echo ""
echo "Frontend Service:"
echo "  Latest: ${ECR_REGISTRY}/yendorcats-frontend:latest"
echo "  SHA: ${ECR_REGISTRY}/yendorcats-frontend:${GIT_SHA_SHORT}"
echo "  Branch: ${ECR_REGISTRY}/yendorcats-frontend:${GIT_BRANCH}"
echo ""
echo "Uploader Service:"
echo "  Latest: ${ECR_REGISTRY}/yendorcats-uploader:latest"
echo "  SHA: ${ECR_REGISTRY}/yendorcats-uploader:${GIT_SHA_SHORT}"
echo "  Branch: ${ECR_REGISTRY}/yendorcats-uploader:${GIT_BRANCH}"
echo ""
echo -e "${GREEN}All images are ready for deployment!${NC}"

# Clean up
rm -rf ${TEMP_DOCKER_CONFIG}
unset DOCKER_CONFIG
unset AWS_ACCESS_KEY_ID
unset AWS_SECRET_ACCESS_KEY
unset AWS_DEFAULT_REGION

echo ""
echo -e "${YELLOW}Cleaned up temporary configurations${NC}"
