name: Build and Push All Services (API, Frontend, Uploader)

on:
  push:
    branches: [ main ]
    paths:
      - 'frontend/**'
      - 'backend/**'
      - 'tools/file-uploader/**'
      - 'Dockerfile.frontend.ci'
      - '.github/workflows/frontend-ci.yml'
  workflow_dispatch: {}

jobs:
  build-and-push-all:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      id-token: write

    env:
      AWS_REGION: ap-southeast-2
      ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.ap-southeast-2.amazonaws.com
      SHA_SHORT: ${{ github.sha }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0   # ensure .git is present for Dockerfile.frontend.ci

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Compute short SHA tag
        id: vars
        run: |
          echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Build and push API image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: backend/YendorCats.API/Dockerfile
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/yendorcats-api:${{ steps.vars.outputs.sha_short }}
            ${{ env.ECR_REGISTRY }}/yendorcats-api:latest

      - name: Build and push frontend image (auto-stamped from .git)
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile.frontend.ci
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/yendorcats-frontend:${{ steps.vars.outputs.sha_short }}
            ${{ env.ECR_REGISTRY }}/yendorcats-frontend:latest

      - name: Build and push uploader image
        uses: docker/build-push-action@v5
        with:
          context: tools/file-uploader
          file: tools/file-uploader/Dockerfile
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/yendorcats-uploader:${{ steps.vars.outputs.sha_short }}
            ${{ env.ECR_REGISTRY }}/yendorcats-uploader:latest

      - name: Output image tags
        run: |
          echo "Built and pushed all services with tag: ${{ steps.vars.outputs.sha_short }}"
          echo "API: ${{ env.ECR_REGISTRY }}/yendorcats-api:${{ steps.vars.outputs.sha_short }}"
          echo "Frontend: ${{ env.ECR_REGISTRY }}/yendorcats-frontend:${{ steps.vars.outputs.sha_short }}"
          echo "Uploader: ${{ env.ECR_REGISTRY }}/yendorcats-uploader:${{ steps.vars.outputs.sha_short }}"

  # Optional: deploy job (uncomment and adapt to your server)
  # deploy:
  #   needs: build-and-push-all
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: SSH to server and redeploy all services
  #       uses: appleboy/ssh-action@v1.0.3
  #       with:
  #         host: ${{ secrets.DEPLOY_HOST }}
  #         username: ${{ secrets.DEPLOY_USER }}
  #         key: ${{ secrets.DEPLOY_SSH_KEY }}
  #         script: |
  #           export REGISTRY=${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.ap-southeast-2.amazonaws.com
  #           export IMAGE_TAG=${{ steps.vars.outputs.sha_short }}
  #           cd /path/to/your/compose/directory
  #           docker compose -f docker-compose.production.yml pull
  #           docker compose -f docker-compose.production.yml up -d

