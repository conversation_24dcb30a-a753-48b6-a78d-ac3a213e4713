using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using YendorCats.API.Data;
using YendorCats.API.Models;

namespace YendorCats.API.Services
{
    /// <summary>
    /// Smart image loading service implementation
    /// Implements priority chain: B2 Direct → Local Resources → Database Cache
    /// </summary>
    public class SmartImageService : ISmartImageService
    {
        private readonly ILogger<SmartImageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IPhotoIndexService _photoIndexService;
        private readonly AppDbContext _context;
        private readonly HttpClient _httpClient;
        private readonly IS3StorageService _s3StorageService;
        private readonly string _b2PublicUrlTemplate;
        private readonly string _localResourceUrlTemplate;

        public SmartImageService(
            ILogger<SmartImageService> logger,
            IConfiguration configuration,
            IPhotoIndexService photoIndexService,
            AppDbContext context,
            HttpClient httpClient,
            IS3StorageService s3StorageService)
        {
            _logger = logger;
            _configuration = configuration;
            _photoIndexService = photoIndexService;
            _context = context;
            _httpClient = httpClient;
            _s3StorageService = s3StorageService;

            // Configure HTTP client for B2 checks
            _httpClient.Timeout = TimeSpan.FromSeconds(3); // Quick timeout for availability checks

            // Get URL templates from configuration
            _b2PublicUrlTemplate = _configuration["AWS:S3:PublicUrl"] ?? "https://f004.backblazeb2.com/file/yendor/{key}";
            _localResourceUrlTemplate = "/frontend/resources/{category}/{filename}";

            _logger.LogInformation("SmartImageService initialized with B2 template: {Template}", _b2PublicUrlTemplate);
        }

        public async Task<SmartImageResponse> GetCategoryImagesAsync(string category, string sortBy = "date", bool includeMetadata = false)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("*** TESTING *** Smart loading category: {Category} with includeMetadata={IncludeMetadata}", category, includeMetadata);

                // Get images from database first (for metadata)
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.Category.ToLower() == category.ToLower())
                    .ToListAsync();

                List<CatGalleryImage> sourceImages;
                string sourceType;

                if (dbImages.Any())
                {
                    sourceImages = SortImages(dbImages, sortBy);
                    sourceType = "database";
                    _logger.LogInformation("Found {Count} images in database for category {Category}", dbImages.Count, category);
                }
                else
                {
                    // Fallback to photo index
                    sourceImages = await _photoIndexService.GetCategoryPhotosAsync(category);
                    sourceImages = SortImages(sourceImages, sortBy);
                    sourceType = "index";
                    _logger.LogInformation("Found {Count} images in photo index for category {Category}", sourceImages.Count, category);
                }

                // Convert to smart image info with fallback URLs
                var smartImages = new List<SmartImageInfo>();
                var stats = new SmartLoadingStats
                {
                    TotalImages = sourceImages.Count,
                    PreferredSource = "B2"
                };

                foreach (var image in sourceImages)
                {
                    var smartImage = await CreateSmartImageInfo(image, includeMetadata);
                    smartImages.Add(smartImage);

                    // Update stats
                    if (smartImage.IsAvailableOnB2) stats.B2Available++;
                    if (smartImage.IsAvailableLocally) stats.LocalAvailable++;
                    if (smartImage.IsAvailableInDatabase) stats.DatabaseOnly++;
                }

                stopwatch.Stop();
                stats.LoadingTime = stopwatch.Elapsed;

                return new SmartImageResponse
                {
                    Category = category,
                    Count = smartImages.Count,
                    SortBy = sortBy,
                    Images = smartImages,
                    Source = sourceType,
                    LoadingStats = stats,
                    RetrievedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in smart loading for category: {Category}", category);
                throw;
            }
        }

        public async Task<SmartImageResponse> GetCatImagesAsync(string catName, string sortBy = "date", bool includeMetadata = false)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Smart loading cat images: {CatName}", catName);

                // Get images from database first
                var dbImages = await _context.CatGalleryImages
                    .Where(img => img.CatName.ToLower() == catName.ToLower())
                    .ToListAsync();

                List<CatGalleryImage> sourceImages;
                string sourceType;

                if (dbImages.Any())
                {
                    sourceImages = SortImages(dbImages, sortBy);
                    sourceType = "database";
                }
                else
                {
                    // Fallback to photo index
                    sourceImages = await _photoIndexService.GetCatPhotosAsync(catName);
                    sourceImages = SortImages(sourceImages, sortBy);
                    sourceType = "index";
                }

                // Convert to smart image info
                var smartImages = new List<SmartImageInfo>();
                var stats = new SmartLoadingStats
                {
                    TotalImages = sourceImages.Count,
                    PreferredSource = "B2"
                };

                foreach (var image in sourceImages)
                {
                    var smartImage = await CreateSmartImageInfo(image, includeMetadata);
                    smartImages.Add(smartImage);

                    // Update stats
                    if (smartImage.IsAvailableOnB2) stats.B2Available++;
                    if (smartImage.IsAvailableLocally) stats.LocalAvailable++;
                    if (smartImage.IsAvailableInDatabase) stats.DatabaseOnly++;
                }

                stopwatch.Stop();
                stats.LoadingTime = stopwatch.Elapsed;

                return new SmartImageResponse
                {
                    CatName = catName,
                    Category = sourceImages.FirstOrDefault()?.Category ?? "",
                    Count = smartImages.Count,
                    SortBy = sortBy,
                    Images = smartImages,
                    Source = sourceType,
                    LoadingStats = stats,
                    RetrievedAt = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in smart loading for cat: {CatName}", catName);
                throw;
            }
        }

        public async Task<bool> CheckImageAvailabilityAsync(string b2Url)
        {
            try
            {
                using var request = new HttpRequestMessage(HttpMethod.Head, b2Url);
                using var response = await _httpClient.SendAsync(request);
                
                var isAvailable = response.IsSuccessStatusCode;
                _logger.LogDebug("B2 availability check for {Url}: {Available}", b2Url, isAvailable);
                
                return isAvailable;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking B2 availability for URL: {Url}", b2Url);
                return false;
            }
        }

        public async Task<SmartImageInfo> GetImageWithFallbackAsync(string category, string catName, string filename)
        {
            try
            {
                // Try to find in database first
                var dbImage = await _context.CatGalleryImages
                    .FirstOrDefaultAsync(img => 
                        img.Category.ToLower() == category.ToLower() &&
                        img.CatName.ToLower() == catName.ToLower() &&
                        (img.OriginalFileName == filename || img.Filename == filename));

                if (dbImage != null)
                {
                    return await CreateSmartImageInfo(dbImage, true);
                }

                // Create a synthetic image info for fallback
                var smartImage = new SmartImageInfo
                {
                    CatName = catName,
                    Category = category,
                    DateTaken = DateTime.UtcNow,
                    PrimaryUrl = GenerateB2DirectUrl(category, catName, filename),
                    FallbackUrl = GenerateLocalResourceUrl(category, filename),
                    DatabaseUrl = "",
                    IsAvailableInDatabase = false
                };

                // Check B2 availability
                smartImage.IsAvailableOnB2 = await CheckImageAvailabilityAsync(smartImage.PrimaryUrl);
                smartImage.ActiveUrl = smartImage.IsAvailableOnB2 ? smartImage.PrimaryUrl : smartImage.FallbackUrl;

                return smartImage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting image with fallback: {Category}/{CatName}/{Filename}", category, catName, filename);
                throw;
            }
        }

        public string GenerateB2DirectUrl(string category, string catName, string filename)
        {
            var s3Key = $"{category}/{catName}/{filename}";
            return _b2PublicUrlTemplate.Replace("{key}", s3Key);
        }

        public string GenerateLocalResourceUrl(string category, string filename)
        {
            return _localResourceUrlTemplate
                .Replace("{category}", category)
                .Replace("{filename}", filename);
        }

        private async Task<SmartImageInfo> CreateSmartImageInfo(CatGalleryImage image, bool includeMetadata)
        {
            _logger.LogInformation("CreateSmartImageInfo called for image {ImageId} with includeMetadata={IncludeMetadata}",
                image.Id, includeMetadata);
            // PRIORITY SYSTEM: Check S3 metadata first, then fallback to database values
            string catName = image.CatName ?? "Unknown";
            string description = image.Description;
            string breed = image.Breed;
            string gender = image.Gender;
            string ageAtPhoto = image.AgeAtPhoto;

            // Always try to get S3 metadata if we have a storage key - S3 metadata takes priority
            bool shouldCheckS3Metadata = true;

            // Extract storage key from URL if StorageKey field is empty
            string storageKey = image.StorageKey;
            if (string.IsNullOrEmpty(storageKey) && !string.IsNullOrEmpty(image.ImageUrl))
            {
                // Extract key from B2 URL: https://f004.backblazeb2.com/file/yendor/YendorCats-General-SiteAccess/queens/IMG_9260.jpg
                var match = System.Text.RegularExpressions.Regex.Match(image.ImageUrl, @"/file/[^/]+/(.+)$");
                if (match.Success)
                {
                    storageKey = match.Groups[1].Value;
                }
            }

            _logger.LogInformation("Checking image {StorageKey}: CatName='{CatName}', CheckingS3Metadata={CheckingS3Metadata}",
                storageKey, image.CatName, shouldCheckS3Metadata);

            if (!string.IsNullOrEmpty(storageKey) && shouldCheckS3Metadata)
            {
                try
                {
                    var s3Metadata = await _s3StorageService.GetObjectMetadataAsync(storageKey);
                    _logger.LogInformation("Retrieved S3 metadata for {StorageKey}: Count={Count}, Keys=[{Keys}]",
                        storageKey, s3Metadata?.Count ?? 0, s3Metadata != null ? string.Join(", ", s3Metadata.Keys) : "null");

                    // Log all metadata key-value pairs for debugging
                    if (s3Metadata != null && s3Metadata.Count > 0)
                    {
                        foreach (var kvp in s3Metadata)
                        {
                            _logger.LogInformation("S3 Metadata: {Key} = {Value}", kvp.Key, kvp.Value);
                        }
                    }

                    if (s3Metadata != null && s3Metadata.Count > 0)
                    {
                        // Extract metadata with priority over database values
                        var s3CatName = GetMetadataValue(s3Metadata, "cat-name", "name", "catname");
                        if (!string.IsNullOrEmpty(s3CatName))
                        {
                            catName = s3CatName;
                        }

                        var s3Description = GetMetadataValue(s3Metadata, "description", "desc");
                        if (!string.IsNullOrEmpty(s3Description))
                        {
                            description = s3Description;
                        }

                        var s3Breed = GetMetadataValue(s3Metadata, "breed", "cat-breed");
                        if (!string.IsNullOrEmpty(s3Breed))
                        {
                            breed = s3Breed;
                        }

                        var s3Gender = GetMetadataValue(s3Metadata, "gender", "sex");
                        if (!string.IsNullOrEmpty(s3Gender))
                        {
                            gender = s3Gender;
                        }

                        var s3Age = GetMetadataValue(s3Metadata, "age", "age-at-photo", "cat-age");
                        if (!string.IsNullOrEmpty(s3Age))
                        {
                            ageAtPhoto = s3Age;
                        }

                        // Extract date taken from S3 metadata
                        var s3DateTaken = GetMetadataValue(s3Metadata, "date-taken", "photo-date", "taken-date");
                        if (!string.IsNullOrEmpty(s3DateTaken) && DateTime.TryParse(s3DateTaken, out var parsedDateTaken))
                        {
                            image.DateTaken = parsedDateTaken;
                        }

                        _logger.LogInformation("Using S3 metadata for {StorageKey}: CatName='{CatName}', Breed='{Breed}', Gender='{Gender}', Age='{Age}', DateTaken='{DateTaken}'",
                            image.StorageKey, catName, breed, gender, ageAtPhoto, s3DateTaken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to retrieve S3 metadata for {StorageKey}, using database values", image.StorageKey);
                }
            }

            var smartImage = new SmartImageInfo
            {
                Id = image.Id,
                CatName = catName,
                Category = image.Category,
                DateTaken = image.DateTaken ?? DateTime.MinValue,
                OrderNumber = image.DisplayOrder,
                Age = image.Age,
                DatabaseUrl = image.ImageUrl,
                IsAvailableInDatabase = !string.IsNullOrEmpty(image.ImageUrl)
            };

            // Generate B2 direct URL
            if (!string.IsNullOrEmpty(image.StorageKey))
            {
                smartImage.PrimaryUrl = _b2PublicUrlTemplate.Replace("{key}", image.StorageKey);
            }
            else if (!string.IsNullOrEmpty(image.CatName))
            {
                // Generate from filename if we have it
                var filename = image.OriginalFileName ?? image.Filename ?? $"{image.CatName}.jpg";
                smartImage.PrimaryUrl = GenerateB2DirectUrl(image.Category, image.CatName, filename);
            }

            // Generate local resource URL
            var localFilename = image.OriginalFileName ?? image.Filename ?? $"{image.CatName}.jpg";
            smartImage.FallbackUrl = GenerateLocalResourceUrl(image.Category, localFilename);

            // For performance, we'll assume B2 is available and let the frontend check
            // In a production system, you might want to implement caching for availability checks
            smartImage.IsAvailableOnB2 = true; // Optimistic assumption
            smartImage.IsAvailableLocally = false; // Would need file system check

            // Set active URL (prefer B2)
            smartImage.ActiveUrl = !string.IsNullOrEmpty(smartImage.PrimaryUrl) ? smartImage.PrimaryUrl : smartImage.DatabaseUrl;

            // Include metadata if requested (use S3-prioritized values)
            if (includeMetadata)
            {
                smartImage.Description = description;
                smartImage.Color = image.Color;
                smartImage.Gender = gender;
                smartImage.Traits = image.Traits;
                smartImage.Breed = breed;
                smartImage.Personality = image.Personality;
                smartImage.FileFormat = image.FileFormat ?? image.Format;
                smartImage.FileSize = image.FileSize > 0 ? image.FileSize : null;
                smartImage.Width = image.Width > 0 ? image.Width : null;
                smartImage.Height = image.Height > 0 ? image.Height : null;
            }

            return smartImage;
        }

        private List<CatGalleryImage> SortImages(List<CatGalleryImage> images, string sortBy)
        {
            return sortBy.ToLower() switch
            {
                "date" => images.OrderByDescending(img => img.DateTaken).ToList(),
                "name" => images.OrderBy(img => img.CatName).ThenBy(img => img.DateTaken).ToList(),
                "random" => images.OrderBy(x => Guid.NewGuid()).ToList(),
                _ => images.OrderBy(img => img.CatName).ThenBy(img => img.DateTaken).ToList()
            };
        }

        /// <summary>
        /// Check if image data looks like fallback data that should be overwritten with S3 metadata
        /// </summary>
        private bool IsLikelyFallbackData(CatGalleryImage image)
        {
            // Check for fallback cat names
            if (!string.IsNullOrEmpty(image.CatName))
            {
                var lowerName = image.CatName.ToLower();
                if (lowerName.Contains("beautiful queen") ||
                    lowerName.Contains("beautiful stud") ||
                    lowerName.Contains("beautiful kitten") ||
                    lowerName.StartsWith("img_") ||
                    lowerName.StartsWith("dsc_") ||
                    lowerName == "yendorcats")
                {
                    return true;
                }
            }

            // Check if breed is just the default and other metadata is missing
            if (image.Breed == "Maine Coon" && string.IsNullOrEmpty(image.Description) &&
                string.IsNullOrEmpty(image.Tags) && string.IsNullOrEmpty(image.Gender))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Get metadata value by checking multiple possible keys (case-insensitive)
        /// </summary>
        private string GetMetadataValue(Dictionary<string, string> metadata, params string[] possibleKeys)
        {
            foreach (var key in possibleKeys)
            {
                // Check exact match first
                if (metadata.TryGetValue(key, out var value) && !string.IsNullOrWhiteSpace(value))
                    return value.Trim();

                // Check case-insensitive match
                var kvp = metadata.FirstOrDefault(m => string.Equals(m.Key, key, StringComparison.OrdinalIgnoreCase));
                if (!string.IsNullOrEmpty(kvp.Key) && !string.IsNullOrWhiteSpace(kvp.Value))
                    return kvp.Value.Trim();
            }
            return null;
        }
    }
}
